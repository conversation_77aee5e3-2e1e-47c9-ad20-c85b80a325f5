package mastermind

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/run-bigpig/hongdou/internal/consts"
	"github.com/run-bigpig/hongdou/internal/pkg/agent"
	"github.com/run-bigpig/hongdou/internal/pkg/structuredoutput"
	"github.com/run-bigpig/hongdou/internal/provider"
	"github.com/run-bigpig/hongdou/internal/service"
	"github.com/run-bigpig/hongdou/internal/service/engine"
	"github.com/run-bigpig/hongdou/internal/service/message"
	"github.com/run-bigpig/hongdou/internal/svc"
	"github.com/run-bigpig/hongdou/internal/types"
	"github.com/run-bigpig/hongdou/internal/utils"
	"github.com/zeromicro/go-zero/core/logx"
	"log"
)

type Mastermind struct {
	ctx            context.Context
	svcCtx         *svc.ServiceContext
	logger         logx.Logger
	agent          *agent.Agent
	progress<PERSON>han   chan<- types.StreamResponse
	messageManager *message.Message
}

func NewMastermind(ctx context.Context, svcCtx *svc.ServiceContext, stream chan<- types.StreamResponse) *Mastermind {
	m := &Mastermind{
		ctx:            ctx,
		svcCtx:         svcCtx,
		logger:         logx.WithContext(ctx),
		progressChan:   stream,
		messageManager: message.NewMessage(ctx, svcCtx.Memory),
	}
	m.loadAgent(svcCtx.Config.MasterMind.LLM)
	return m
}

func (m *Mastermind) loadAgent(llm string) {
	a, err := agent.NewAgent(
		agent.WithLLM(provider.NewProvider(m.ctx, m.svcCtx, llm)),
		agent.WithSystemPrompt(m.svcCtx.Config.MasterMind.Prompt),
		agent.WithName("HongDouMind"),
		agent.WithResponseFormat(*structuredoutput.NewResponseFormat(service.StructOutput{})),
	)
	if err != nil {
		log.Fatalf("new agent error: %v", err)
	}
	m.agent = a
}

// Run 执行主脑思考规划
func (m *Mastermind) Run(question string) (string, error) {
	// Send progress update if streaming is enabled
	m.sendProgress("正在获取可用工具信息...")

	// Get available tools for context
	availableTools := m.getAvailableToolsContext()

	// Send progress update if streaming is enabled
	m.sendProgress("正在构建增强提示...")

	// Create enhanced prompt with tool context
	enhancedQuestion := m.buildEnhancedPrompt(question, availableTools)

	// Send progress update if streaming is enabled
	m.sendProgress("主脑正在深度思考和决策...")
	response, err := m.agent.Run(m.ctx, enhancedQuestion)
	if err != nil {
		return "", err
	}
	m.logger.Debugf("=========================Mastermind response: %s", response)
	var jsonData service.StructOutput
	err = json.Unmarshal([]byte(response), &jsonData)
	if err != nil {
		return "", err
	}

	// Handle different dispatch modes
	switch jsonData.DispatchMode {
	case consts.MasterMindDispatchMode_DIRECT_ANSWER:
		m.sendProgress("主脑判断为简单问题，直接回答...")
		// 记录助手消息
		m.messageManager.AddAssistantMessage(jsonData.Answer)
		return jsonData.Answer, nil

	case consts.MasterMindDispatchMode_AGENT_EXECUTION:
		m.sendProgress(fmt.Sprintf("主脑制定了执行计划，正在启动 %d 个专业智能体...", len(jsonData.SubAgents)))

		// Set the SubQuestion for each agent if not already set
		for i := range jsonData.SubAgents {
			if jsonData.SubAgents[i].SubQuestion == "" {
				jsonData.SubAgents[i].SubQuestion = question
			}
		}
		agentResult, err := engine.NewExecutionEngine(m.ctx, m.svcCtx, m.progressChan).ExecuteMultiAgent(&engine.ExecutionPlan{
			SubAgents:            jsonData.SubAgents,
			FinalSynthesisPrompt: jsonData.FinalSynthesisPrompt,
		})
		if err != nil {
			return "", err
		}
		// 记录助手消息
		m.messageManager.AddAssistantMessage(agentResult)
		return agentResult, nil
	default:
		return "", errors.New("unknown dispatch mode: " + string(jsonData.DispatchMode))
	}
}

// getAvailableToolsContext returns a context string describing available tools
func (m *Mastermind) getAvailableToolsContext() string {
	// Use semantic tool discovery if available
	if m.svcCtx.Tools != nil {
		return m.svcCtx.Tools.GetToolsContext("general query", 10)
	}
	return ""
}

// buildEnhancedPrompt creates an enhanced prompt with tool context
func (m *Mastermind) buildEnhancedPrompt(question, toolsContext string) string {
	query := fmt.Sprintf("User Query: %s\n%s\nPlease analyze the user query and decide on the appropriate dispatch mode and execution plan.", question, toolsContext)
	//获取历史消息
	if len(m.messageManager.GetMessages()) > 0 {
		return fmt.Sprintf("%s\nuser:%s", agent.FormatHistoryIntoPrompt(m.messageManager.GetMessages()), query)
	}
	//记录当前用户问题消息
	m.messageManager.AddUserMessage(question)
	return query
}

func (m *Mastermind) sendProgress(message string) {
	utils.SendProgress(m.progressChan, message)
}
