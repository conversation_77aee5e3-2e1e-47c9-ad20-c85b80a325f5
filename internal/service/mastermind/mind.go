package mastermind

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/run-bigpig/hongdou/internal/consts"
	"github.com/run-bigpig/hongdou/internal/pkg/agent"
	"github.com/run-bigpig/hongdou/internal/pkg/structuredoutput"
	"github.com/run-bigpig/hongdou/internal/provider"
	"github.com/run-bigpig/hongdou/internal/service"
	"github.com/run-bigpig/hongdou/internal/service/engine"
	"github.com/run-bigpig/hongdou/internal/service/message"
	"github.com/run-bigpig/hongdou/internal/svc"
	"github.com/run-bigpig/hongdou/internal/types"
	"github.com/run-bigpig/hongdou/internal/utils"
	"github.com/zeromicro/go-zero/core/logx"
	"log"
)

type Mastermind struct {
	ctx            context.Context
	svcCtx         *svc.ServiceContext
	logger         logx.Logger
	agent          *agent.Agent
	progress<PERSON>han   chan<- types.StreamResponse
	messageManager *message.Message
}

func NewMastermind(ctx context.Context, svcCtx *svc.ServiceContext, stream chan<- types.StreamResponse) *Mastermind {
	m := &Mastermind{
		ctx:            ctx,
		svcCtx:         svcCtx,
		logger:         logx.WithContext(ctx),
		progressChan:   stream,
		messageManager: message.NewMessage(ctx, svcCtx.Memory),
	}
	m.loadAgent(svcCtx.Config.MasterMind.LLM)
	return m
}

func (m *Mastermind) loadAgent(llm string) {
	a, err := agent.NewAgent(
		agent.WithLLM(provider.NewProvider(m.ctx, m.svcCtx, llm)),
		agent.WithSystemPrompt(m.svcCtx.Config.MasterMind.Prompt),
		agent.WithName("HongDouMind"),
		agent.WithResponseFormat(*structuredoutput.NewResponseFormat(service.StructOutput{})),
	)
	if err != nil {
		log.Fatalf("new agent error: %v", err)
	}
	m.agent = a
}

// Run 执行主脑思考规划
func (m *Mastermind) Run(question string) (string, error) {
	// Send progress update if streaming is enabled
	m.sendProgress("🐾 红豆正在嗅探工具箱喵~ 辛巴你这个大头猫别把工具都弄乱了！让我来找找有什么好用的...")

	// Get available tools for context
	availableTools := m.getAvailableToolsContext()

	// Send progress update if streaming is enabled
	m.sendProgress("✨ 红豆正在精心构建提示喵~ 辛巴你快看，我要施展魔法让问题变清晰！别偷吃我的小鱼干啦！")

	// Create enhanced prompt with tool context
	enhancedQuestion := m.buildEnhancedPrompt(question, availableTools)

	// Send progress update if streaming is enabled
	m.sendProgress("🧠 红豆的小脑瓜正在飞速运转喵~ 辛巴你别在那边追自己的尾巴了，我在认真思考呢！嘿嘿，看我的智慧...")
	response, err := m.agent.Run(m.ctx, enhancedQuestion)
	if err != nil {
		return "", err
	}
	m.logger.Debugf("=========================Mastermind response: %s", response)
	var jsonData service.StructOutput
	err = json.Unmarshal([]byte(response), &jsonData)
	if err != nil {
		return "", err
	}

	// Handle different dispatch modes
	switch jsonData.DispatchMode {
	case consts.MasterMindDispatchMode_DIRECT_ANSWER:
		m.sendProgress("😸 红豆觉得这个问题太简单啦喵~ 辛巴你看，连你这个笨笨的大头猫都能懂！让我直接告诉主人答案吧~")
		// 记录助手消息
		m.messageManager.AddAssistantMessage(jsonData.Answer)
		return jsonData.Answer, nil

	case consts.MasterMindDispatchMode_AGENT_EXECUTION:
		m.sendProgress(fmt.Sprintf("🚀 红豆制定了超棒的计划喵~ 辛巴！快叫你的 %d 个分身来帮忙！别偷懒哦，不然我就藏起你的猫薄荷！", len(jsonData.SubAgents)))

		// Set the SubQuestion for each agent if not already set
		for i := range jsonData.SubAgents {
			if jsonData.SubAgents[i].SubQuestion == "" {
				jsonData.SubAgents[i].SubQuestion = question
			}
		}
		agentResult, err := engine.NewExecutionEngine(m.ctx, m.svcCtx, m.progressChan).ExecuteMultiAgent(&engine.ExecutionPlan{
			SubAgents:            jsonData.SubAgents,
			FinalSynthesisPrompt: jsonData.FinalSynthesisPrompt,
		})
		if err != nil {
			return "", err
		}
		// 记录助手消息
		m.messageManager.AddAssistantMessage(agentResult)
		return agentResult, nil
	default:
		return "", errors.New("unknown dispatch mode: " + string(jsonData.DispatchMode))
	}
}

// getAvailableToolsContext returns a context string describing available tools
func (m *Mastermind) getAvailableToolsContext() string {
	// Use semantic tool discovery if available
	if m.svcCtx.Tools != nil {
		return m.svcCtx.Tools.GetToolsContext("general query", 10)
	}
	return ""
}

// buildEnhancedPrompt creates an enhanced prompt with tool context
func (m *Mastermind) buildEnhancedPrompt(question, toolsContext string) string {
	query := fmt.Sprintf("User Query: %s\n%s\nPlease analyze the user query and decide on the appropriate dispatch mode and execution plan.", question, toolsContext)
	//获取历史消息
	if len(m.messageManager.GetMessages()) > 0 {
		return fmt.Sprintf("%s\nuser:%s", agent.FormatHistoryIntoPrompt(m.messageManager.GetMessages()), query)
	}
	return query
}

func (m *Mastermind) sendProgress(message string) {
	utils.SendProgress(m.progressChan, message)
}
