package handler

import (
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"github.com/run-bigpig/hongdou/internal/logic"
	"github.com/run-bigpig/hongdou/internal/svc"
	"github.com/run-bigpig/hongdou/internal/types"
	"github.com/zeromicro/go-zero/rest/httpx"
)

// 聊天 - Unified handler supporting both streaming and non-streaming responses
func ChatHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.ChatRequest
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		// Check if streaming is requested
		if req.Stream {
			handleStreamingChat(w, r, &req, svcCtx)
		} else {
			handleNonStreamingChat(w, r, &req, svcCtx)
		}
	}
}

// handleNonStreamingChat handles traditional non-streaming chat requests
func handleNonStreamingChat(w http.ResponseWriter, r *http.Request, req *types.ChatRequest, svcCtx *svc.ServiceContext) {
	l := logic.NewChatLogic(r.Context(), svcCtx)
	resp, err := l.Chat(req, nil)
	if err != nil {
		httpx.ErrorCtx(r.Context(), w, err)
	} else {
		httpx.OkJsonCtx(r.Context(), w, resp)
	}
}

// handleStreamingChat handles streaming chat requests with Server-Sent Events
func handleStreamingChat(w http.ResponseWriter, r *http.Request, req *types.ChatRequest, svcCtx *svc.ServiceContext) {
	// Set headers for Server-Sent Events
	w.Header().Set("Content-Type", "text/event-stream")
	w.Header().Set("Cache-Control", "no-cache")
	w.Header().Set("Connection", "keep-alive")
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.Header().Set("Access-Control-Allow-Headers", "Cache-Control")

	// Create a flusher to send data immediately
	flusher, ok := w.(http.Flusher)
	if !ok {
		http.Error(w, "Streaming unsupported", http.StatusInternalServerError)
		return
	}

	// Create streaming logic
	l := logic.NewChatLogic(r.Context(), svcCtx)

	// Create a channel for progress updates
	progressChan := make(chan types.StreamResponse, 10)

	// Start processing in a goroutine
	go func() {
		defer close(progressChan)

		// Send initial progress
		progressChan <- types.StreamResponse{
			Type:    "progress",
			Content: "正在分析您的请求...",
			Done:    false,
		}

		// Process the request
		result, err := l.Chat(req, progressChan)

		if err != nil {
			progressChan <- types.StreamResponse{
				Type:    "error",
				Content: err.Error(),
				Done:    true,
			}
			return
		}

		// Send final result
		progressChan <- types.StreamResponse{
			Type:    "result",
			Content: result.Message,
			Done:    true,
		}
	}()

	// Stream the responses
	for response := range progressChan {
		data, err := json.Marshal(response)
		if err != nil {
			continue
		}

		fmt.Fprintf(w, "data: %s\n\n", data)
		flusher.Flush()

		// Add a small delay to make streaming visible
		time.Sleep(100 * time.Millisecond)

		if response.Done {
			break
		}
	}
}
