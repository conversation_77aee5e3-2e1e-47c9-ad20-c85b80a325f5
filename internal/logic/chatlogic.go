package logic

import (
	"context"
	"github.com/run-bigpig/hongdou/internal/pkg/memory"
	"github.com/run-bigpig/hongdou/internal/pkg/multitenancy"
	"github.com/run-bigpig/hongdou/internal/service/mastermind"

	"github.com/run-bigpig/hongdou/internal/svc"
	"github.com/run-bigpig/hongdou/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type ChatLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 聊天
func NewChatLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ChatLogic {
	return &ChatLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ChatLogic) Chat(req *types.ChatRequest, progress<PERSON>han chan<- types.StreamResponse) (resp *types.ChatResponse, err error) {
	l.ctx = multitenancy.WithOrgID(l.ctx, "hongdou-org")
	l.ctx = context.WithValue(l.ctx, memory.ConversationIDKey, req.ConversationId)
	//初始化主脑
	m := mastermind.NewMastermind(l.ctx, l.svcCtx, progressChan)
	//主脑进行思考
	think, err := m.Run(req.Message)
	if err != nil {
		l.Logger.Errorf("mastermind run error: %v", err)
		return nil, err
	}
	return &types.ChatResponse{
		Message: think,
	}, nil
}
